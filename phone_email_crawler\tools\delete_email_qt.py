# -*- coding: utf-8 -*-
"""
邮箱数据删除工具
Author: jian wei
Create Time: 2025/8/18
File Name: delete_email_qt.py
"""
import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QSpinBox, QRadioButton,
                             QDateTimeEdit, QPushButton, QProgressBar,
                             QTextEdit, QGroupBox, QButtonGroup, QMessageBox, QDesktopWidget)
from PyQt5.QtCore import QDateTime, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from sqlalchemy import text
import time
from datetime import datetime

# 数据库连接配置（写死配置，用于exe打包）
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 数据库配置
MYSQL_HOST = '*************'
MYSQL_PORT = '13306'
MYSQL_USER = 'root'
MYSQL_PASSWORD = 'qwer1234'
MYSQL_DATABASE = 'phone_email'

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"

engine = create_engine(
    DATABASE_URL,
    pool_recycle=1800,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    echo=False
)

# 创建Session工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=True, bind=engine, expire_on_commit=False)

def get_db_session():
    """获取数据库会话"""
    return SessionLocal()


class EmailDeleteWorker(QThread):
    """删除邮箱数据的工作线程"""
    progress_updated = pyqtSignal(int, int, str, str)  # 已删除数量, 总数量, 剩余时间, 速度信息
    finished = pyqtSignal(str)  # 完成信号
    error = pyqtSignal(str)  # 错误信号
    status_updated = pyqtSignal(str)  # 状态更新信号

    def __init__(self, delete_type, id_threshold=None, datetime_threshold=None):
        super().__init__()
        self.delete_type = delete_type  # 'id' 或 'datetime'
        self.batch_size = 10000  # 批次大小（优化性能）
        self.id_threshold = id_threshold
        self.datetime_threshold = datetime_threshold
        self.is_running = True
        
    def get_total_count(self):
        """获取要删除的总记录数"""
        try:
            db = get_db_session()
            if self.delete_type == 'id':
                query = text(f"SELECT COUNT(*) FROM email_results WHERE id <= {self.id_threshold}")
            else:  # datetime
                query = text(f"SELECT COUNT(*) FROM email_results WHERE created_at <= '{self.datetime_threshold}'")

            result = db.execute(query)
            count = result.scalar()
            db.close()
            return count
        except Exception as e:
            self.error.emit(f"获取邮箱记录数失败: {str(e)}")
            return 0
    
    def get_phone_ids_to_delete(self, email_batch_ids):
        """获取要删除的电话记录ID列表"""
        try:
            db = get_db_session()
            if not email_batch_ids:
                db.close()
                return []
            
            # 获取这批邮箱记录关联的电话ID
            ids_str = ','.join(map(str, email_batch_ids))
            query = text(f"""
                SELECT DISTINCT phone_result_id 
                FROM email_results 
                WHERE id IN ({ids_str})
            """)
            
            result = db.execute(query)
            phone_ids = [row[0] for row in result.fetchall()]
            db.close()
            return phone_ids
        except Exception as e:
            self.error.emit(f"获取关联电话ID失败: {str(e)}")
            return []
    
    def delete_batch(self):
        """删除一批数据（优化版本：批量查询和删除）"""
        try:
            db = get_db_session()

            # 1. 获取要删除的邮箱记录ID和对应的电话ID
            if self.delete_type == 'id':
                query = text(f"""
                    SELECT id, phone_result_id FROM email_results
                    WHERE id <= {self.id_threshold}
                    LIMIT {self.batch_size}
                """)
            else:  # datetime
                query = text(f"""
                    SELECT id, phone_result_id FROM email_results
                    WHERE created_at <= '{self.datetime_threshold}'
                    LIMIT {self.batch_size}
                """)

            result = db.execute(query)
            records = result.fetchall()

            if not records:
                db.close()
                return 0, 0, 0

            email_ids = [record[0] for record in records]
            phone_ids = list(set([record[1] for record in records if record[1] is not None]))  # 去重

            # 2. 先删除邮箱记录（子表）
            email_ids_str = ','.join(map(str, email_ids))
            delete_email_query = text(f"DELETE FROM email_results WHERE id IN ({email_ids_str})")
            email_result = db.execute(delete_email_query)
            email_deleted = email_result.rowcount

            # 3. 批量检查电话记录的邮箱引用计数（性能优化）
            phone_deleted = 0
            phone_skipped = 0

            if phone_ids:
                # 批量查询所有phone_id的邮箱引用计数
                phone_ids_str = ','.join(map(str, phone_ids))
                count_query = text(f"""
                    SELECT phone_result_id, COUNT(*) as email_count
                    FROM email_results
                    WHERE phone_result_id IN ({phone_ids_str})
                    GROUP BY phone_result_id
                """)
                count_result = db.execute(count_query)
                phone_email_counts = {row[0]: row[1] for row in count_result.fetchall()}

                # 找出没有邮箱引用的电话ID
                phones_to_delete = []
                for phone_id in phone_ids:
                    if phone_id not in phone_email_counts:  # 没有邮箱引用
                        phones_to_delete.append(phone_id)
                    else:  # 还有邮箱引用
                        phone_skipped += 1

                # 批量删除没有邮箱引用的电话记录
                if phones_to_delete:
                    phones_to_delete_str = ','.join(map(str, phones_to_delete))
                    delete_phone_query = text(f"DELETE FROM phone_results WHERE id IN ({phones_to_delete_str})")
                    phone_result = db.execute(delete_phone_query)
                    phone_deleted = phone_result.rowcount

            db.commit()
            db.close()

            return email_deleted, phone_deleted, phone_skipped

        except Exception as e:
            try:
                db.rollback()
            except:
                pass
            try:
                db.close()
            except:
                pass
            raise e
    
    def run(self):
        """执行删除操作"""
        self.status_updated.emit("📊 正在统计符合条件的记录数...")
        total_count = self.get_total_count()
        if total_count == 0:
            self.finished.emit("❌ 没有找到符合条件的邮箱记录")
            return

        self.status_updated.emit(f"📊 找到 {total_count:,} 条符合条件的邮箱记录，开始删除...")
            
        deleted_count = 0
        total_phone_deleted = 0
        total_phone_skipped = 0
        start_time = time.time()

        try:
            while self.is_running and deleted_count < total_count:
                batch_start_time = time.time()

                # 删除一批数据
                batch_result = self.delete_batch()
                if isinstance(batch_result, tuple) and len(batch_result) == 3:
                    email_deleted, phone_deleted, phone_skipped = batch_result
                    total_phone_deleted += phone_deleted
                    total_phone_skipped += phone_skipped
                elif isinstance(batch_result, tuple) and len(batch_result) == 2:
                    email_deleted, phone_deleted = batch_result
                    total_phone_deleted += phone_deleted
                else:
                    email_deleted = batch_result

                if email_deleted == 0:
                    break

                deleted_count += email_deleted

                # 计算性能指标
                elapsed_time = time.time() - start_time
                batch_time = time.time() - batch_start_time

                if deleted_count > 0:
                    avg_time_per_record = elapsed_time / deleted_count
                    remaining_records = total_count - deleted_count
                    remaining_time = avg_time_per_record * remaining_records
                    remaining_time_str = self.format_time(remaining_time)

                    # 计算处理速度
                    speed = deleted_count / elapsed_time
                    if speed >= 1000:
                        speed_str = f"{speed/1000:.1f}K条/秒"
                    else:
                        speed_str = f"{speed:.1f}条/秒"

                    # 详细状态信息
                    status_info = f"邮箱:{total_phone_deleted} 电话:{total_phone_deleted} 跳过:{total_phone_skipped} 批次耗时:{batch_time:.2f}s"
                else:
                    remaining_time_str = "计算中..."
                    speed_str = "计算中..."
                    status_info = "正在处理..."

                # 发送进度更新信号（增加速度和状态信息）
                self.progress_updated.emit(deleted_count, total_count, remaining_time_str, f"{speed_str} | {status_info}")

                # 极短休息时间，保持高性能
                time.sleep(0.001)
                
        except Exception as e:
            self.error.emit(f"删除过程中出错: {str(e)}")
            return
            
        elapsed_time = time.time() - start_time
        speed = deleted_count / elapsed_time if elapsed_time > 0 else 0
        speed_str = f"{speed/1000:.1f}K条/秒" if speed >= 1000 else f"{speed:.1f}条/秒"

        if self.is_running:
            self.finished.emit(f"🎯 删除完成！\n📧 删除邮箱记录: {deleted_count:,} 条\n📞 删除电话记录: {total_phone_deleted:,} 条\n⚠️ 跳过电话记录: {total_phone_skipped:,} 条 (因为还有其他邮箱引用)\n⏱️ 总耗时: {self.format_time(elapsed_time)}\n🚀 平均速度: {speed_str}")
        else:
            self.finished.emit(f"🛑 删除已停止！\n📧 已删除邮箱记录: {deleted_count:,} 条\n📞 已删除电话记录: {total_phone_deleted:,} 条\n⚠️ 跳过电话记录: {total_phone_skipped:,} 条 (因为还有其他邮箱引用)\n⏱️ 耗时: {self.format_time(elapsed_time)}\n🚀 平均速度: {speed_str}")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}小时{minutes}分钟"
    
    def stop(self):
        """停止删除操作"""
        self.is_running = False


class EmailDeleteMainWindow(QMainWindow):
    """邮箱删除工具主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("邮箱数据删除工具")
        self.resize(800, 600)

        # 将窗口居中显示
        self.center_window()
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 删除条件组
        condition_group = QGroupBox("删除条件")
        condition_layout = QVBoxLayout(condition_group)
        
        # 删除方式选择
        self.delete_type_group = QButtonGroup()

        # 按ID删除
        self.id_radio = QRadioButton("按邮箱ID删除")
        self.id_radio.setChecked(True)  # 默认选择按ID删除
        self.delete_type_group.addButton(self.id_radio)
        condition_layout.addWidget(self.id_radio)

        # ID选择
        id_layout = QHBoxLayout()
        id_layout.addWidget(QLabel("删除邮箱ID小于等于:"))
        self.id_spinbox = QSpinBox()
        self.id_spinbox.setRange(1, 999999999)
        self.id_spinbox.setValue(1000)
        id_layout.addWidget(self.id_spinbox)
        id_layout.addStretch()
        condition_layout.addLayout(id_layout)

        # 按时间删除
        self.datetime_radio = QRadioButton("按创建时间删除")
        self.delete_type_group.addButton(self.datetime_radio)
        condition_layout.addWidget(self.datetime_radio)

        # 时间选择
        datetime_layout = QHBoxLayout()
        datetime_layout.addWidget(QLabel("删除创建时间早于:"))
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setDateTime(QDateTime.currentDateTime())
        self.datetime_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.datetime_edit.setCalendarPopup(True)  # 启用日历弹出
        datetime_layout.addWidget(self.datetime_edit)
        datetime_layout.addStretch()
        condition_layout.addLayout(datetime_layout)
        
        # 添加说明文字
        info_label = QLabel("⚠️ 注意：删除邮箱记录时会智能删除孤立的电话记录\n💡 如果电话记录还有其他邮箱引用，则会跳过删除")
        info_label.setStyleSheet("color: #FF6B35; font-weight: bold; padding: 5px;")
        info_label.setWordWrap(True)
        condition_layout.addWidget(info_label)
        
        main_layout.addWidget(condition_group)

        # 进度显示组
        progress_group = QGroupBox("删除进度")
        progress_layout = QVBoxLayout(progress_group)

        # 进度条
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)

        # 进度信息
        self.progress_label = QLabel("进度: 0/0 (0%)")
        progress_layout.addWidget(self.progress_label)

        # 剩余时间
        self.time_label = QLabel("预计剩余时间: --")
        progress_layout.addWidget(self.time_label)

        # 速度和状态信息
        self.speed_label = QLabel("处理速度: --")
        progress_layout.addWidget(self.speed_label)

        main_layout.addWidget(progress_group)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("开始删除")
        self.start_button.clicked.connect(self.start_delete)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_delete)
        button_layout.addWidget(self.stop_button)

        main_layout.addLayout(button_layout)

        # 日志显示
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        main_layout.addWidget(log_group)

        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)

        # 初始化日志
        self.add_log("📋 邮箱删除工具已启动，点击'开始删除'时才会连接数据库")

    def center_window(self):
        """将窗口居中显示"""
        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def start_delete(self):
        """开始删除操作"""
        try:
            # 获取参数
            if self.id_radio.isChecked():
                delete_type = 'id'
                id_threshold = self.id_spinbox.value()
                datetime_threshold = None
                condition_desc = f"邮箱ID小于等于 {id_threshold}"
            else:
                delete_type = 'datetime'
                datetime_threshold = self.datetime_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
                id_threshold = None
                condition_desc = f"创建时间早于 {datetime_threshold}"

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除符合条件的邮箱记录及其关联的电话记录吗？\n\n删除条件: {condition_desc}\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 创建工作线程
            self.worker = EmailDeleteWorker(delete_type, id_threshold, datetime_threshold)
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.finished.connect(self.delete_finished)
            self.worker.error.connect(self.delete_error)
            self.worker.status_updated.connect(self.add_log)

            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setValue(0)

            # 添加日志
            self.add_log(f"🚀 开始删除操作")
            self.add_log(f"📋 删除条件: {condition_desc}")
            self.add_log(f"📦 批次大小: {self.worker.batch_size:,}")
            self.add_log(f"🔗 正在连接数据库...")

            # 启动线程
            self.worker.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动删除操作失败: {str(e)}")

    def stop_delete(self):
        """停止删除操作"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.add_log("正在停止删除操作...")

    def update_progress(self, deleted_count, total_count, remaining_time, speed_info):
        """更新进度显示"""
        if total_count > 0:
            progress = int((deleted_count / total_count) * 100)
            self.progress_bar.setValue(progress)
            self.progress_label.setText(f"进度: {deleted_count:,}/{total_count:,} ({progress}%)")
        else:
            self.progress_label.setText(f"进度: {deleted_count:,}/0")

        self.time_label.setText(f"预计剩余时间: {remaining_time}")
        self.speed_label.setText(f"处理状态: {speed_info}")

    def delete_finished(self, message):
        """删除完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(message)
        QMessageBox.information(self, "完成", message)

    def delete_error(self, error_message):
        """删除出错"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_log(f"错误: {error_message}")
        QMessageBox.critical(self, "错误", error_message)

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")


def main():
    app = QApplication(sys.argv)

    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    window = EmailDeleteMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
