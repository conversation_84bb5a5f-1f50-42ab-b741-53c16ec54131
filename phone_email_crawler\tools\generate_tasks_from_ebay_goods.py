# -*- coding: utf-8 -*-
"""
Task generation tool for phone and email crawler from eBay goods data
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/19
File Name: generate_tasks_from_ebay_goods.py
"""
import json
import sys
import os
import logging
from sqlalchemy.exc import IntegrityError
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from phone_email_crawler.shared.db.init_db import get_db_session
from phone_email_crawler.shared.db.db_models import SearchTasks

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_keywords_from_ebay_goods():
    """从ebay/ebay_goods.json加载所有关键词

    Returns:
        list: 排序后的关键词列表
    """
    keywords_file = '../../ebay/ebay_goods.json'
    keywords = []

    try:
        with open(keywords_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 确保数据是列表格式
        if not isinstance(data, list):
            logger.error(f"数据格式错误，期望列表格式，实际为: {type(data)}")
            return []

        # 过滤空值并排序，确保结果一致性
        keywords = [keyword.strip() for keyword in data if keyword and keyword.strip()]
        keywords = sorted(list(set(keywords)))  # 去重并排序

        logger.info(f"成功加载{len(keywords)}个关键词")
        return keywords
    except FileNotFoundError:
        logger.error(f"关键词文件不存在: {keywords_file}")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"JSON文件格式错误: {e}")
        return []
    except Exception as e:
        logger.error(f"加载关键词失败: {e}")
        return []


def load_regions():
    """从ebay/county_list.json加载地区信息（混合使用州名+县名）"""
    county_file = '../../ebay/county_list.json'

    try:
        with open(county_file, 'r', encoding='utf-8') as f:
            county_data = json.load(f)

        # 使用集合去重，提高性能
        state_regions = set()
        county_regions = set()

        for state in county_data:
            # 收集州名
            state_name = state.get('sate_name', '').strip()
            if state_name:
                state_regions.add(state_name)

            # 收集县名（去除County和city后缀）
            county_list = state.get('county_list', [])
            for county in county_list:
                county_name = county.get('county_name', '').strip()
                if county_name:
                    clean_name = county_name.replace("County", "").replace("city", "").strip()
                    if clean_name:
                        county_regions.add(clean_name)

        # 混合使用州名+县名
        state_list = sorted(list(state_regions))
        county_list = sorted(list(county_regions))
        regions = state_list + county_list

        logger.info(f"加载地区信息完成，共{len(regions)}个地区（{len(state_list)}个州 + {len(county_list)}个县）")
        return regions

    except FileNotFoundError:
        logger.error(f"地区文件不存在: {county_file}")
        return []
    except Exception as e:
        logger.error(f"加载地区信息失败: {e}")
        return []


def generate_tasks_fast(task_combinations):
    """使用原生SQL快速生成搜索任务，避免重复"""
    db = get_db_session()
    try:
        logger.info(f"准备插入{len(task_combinations)}个搜索任务")

        # 批量查询已存在的组合（更快）
        logger.info("批量查询已存在的任务...")

        # 构建批量查询条件
        conditions = []
        for task in task_combinations:
            keyword = task['keyword'].replace("'", "''")
            region = task['region'].replace("'", "''")
            conditions.append(f"(keyword = '{keyword}' AND region = '{region}')")

        # 分批查询（避免SQL太长）
        existing_combinations = set()
        batch_size = 1000

        for i in range(0, len(conditions), batch_size):
            batch_conditions = conditions[i:i + batch_size]
            if batch_conditions:
                check_sql = f"""
                SELECT CONCAT(keyword, '|', region)
                FROM search_tasks
                WHERE {' OR '.join(batch_conditions)}
                """
                result = db.execute(text(check_sql))
                for row in result:
                    existing_combinations.add(row[0])

                logger.info(f"查询进度: {min(i + batch_size, len(conditions))}/{len(conditions)}")

        # 过滤掉已存在的组合
        new_combinations = []
        for task in task_combinations:
            combination_key = f"{task['keyword']}|{task['region']}"
            if combination_key not in existing_combinations:
                new_combinations.append(task)

        logger.info(f"过滤完成，需要插入{len(new_combinations)}个新任务")

        if not new_combinations:
            logger.info("✅ 没有新任务需要插入")
            return

        # 批量插入新任务
        batch_size = 2000
        inserted_count = 0

        for i in range(0, len(new_combinations), batch_size):
            batch = new_combinations[i:i + batch_size]

            # 构建批量插入的SQL
            values = []
            for task in batch:
                # 转义单引号防止SQL注入
                keyword = task['keyword'].replace("'", "''")
                region = task['region'].replace("'", "''")
                values.append(f"('{keyword}', '{region}', 'pending', 'pending', NOW(), NOW())")

            if values:
                insert_sql = f"""
                INSERT INTO search_tasks
                (keyword, region, search_phone_status, search_email_status, created_at, updated_at)
                VALUES {', '.join(values)}
                """

                result = db.execute(text(insert_sql))
                db.commit()
                inserted_count += result.rowcount

                progress = (i + len(batch)) / len(new_combinations)
                logger.info(f"插入进度：{i + len(batch)}/{len(new_combinations)} ({progress:.2%}) - 本批次插入 {result.rowcount} 条")

        logger.info(f"✅ 任务生成完成！总共插入{inserted_count}个新搜索任务")

    except Exception as e:
        logger.error(f"生成任务失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def main():
    """主函数

    Args:
        slice_range: 切片范围，如 [0, 100000] 或 slice(0, 100000)
    """
    logger.info("🚀 开始从eBay商品数据生成搜索任务")

    # 加载关键词和地区
    keywords = load_keywords_from_ebay_goods()
    regions = load_regions()

    if not keywords or not regions:
        logger.error("❌ 数据加载失败，程序退出")
        return

    # 生成所有任务组合
    task_combinations = []
    for keyword in sorted(keywords):
        for region in sorted(regions):
            task_combinations.append({'keyword': keyword, 'region': region})

    logger.info(f"✅ 生成 {len(task_combinations)} 个任务组合")
    logger.info(f"📋 关键词: {len(keywords)}个, 地区: {len(regions)}个")

    # 插入数据
    import time
    start_time = time.time()
    selected_combinations = task_combinations[10000:150000]
    generate_tasks_fast(selected_combinations)
    logger.info(f"⏱️  耗时: {time.time() - start_time:.2f} 秒")


if __name__ == "__main__":
    main()
