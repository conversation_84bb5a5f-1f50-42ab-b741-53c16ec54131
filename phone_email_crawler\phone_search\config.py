# -*- coding: utf-8 -*-
"""
Configuration for phone search module
Author: ji<PERSON> <PERSON>i
Create Time: 2025/8/18
File Name: config.py
"""

# 电话搜索配置
PHONE_SEARCH_CONFIG = {
    # 线程配置
    'threads': 20,
    'batch_size': 2000,
    'batch_interval': 2,
    
    # 搜索配置
    'max_retries': 10,
    'max_errors_before_proxy_change': 3,
    'save_frequency': 3,
    
    # <PERSON>ie配置
    'max_cookie_failures': 10,
    'cookie_reset_retries': 5,
    'cookie_reset_delay': 2,
    
    # 代理配置
    'proxy_num': 50,
    'proxy_update_interval': 120,
    
    # 日志配置
    'log_file': 'logs/phone_search.log',
    'log_level': 'INFO',
    
    # <PERSON>ie文件前缀
    'cookie_prefix': 'phone_search_',
    
    # 请求配置
    'request_timeout': 10,
    'request_delay_min': 0.5,
    'request_delay_max': 1.5,
}

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
]

# 基础请求头
BASE_HEADERS = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'max-age=0',
    'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
}
