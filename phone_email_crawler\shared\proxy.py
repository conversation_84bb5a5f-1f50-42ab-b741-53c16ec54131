# -*- coding: utf-8 -*-
"""
Proxy management for phone and email crawler
Author: <PERSON><PERSON> <PERSON><PERSON>
Create Time: 2025/8/18
File Name: proxy.py
"""
import time
import random
from typing import Optional, List
import requests
import logging


class ProxyManager:
    """代理管理器"""
    
    def __init__(self,
                 proxy_url="http://list.rola.info:8088/user_get_ip_list?token=xD3WdIuf5zl2ragb1721654350028&qty={num}&country=&state=&city=&time={minute}&format=txt&protocol=http&filter=1",
                 num=1, minute=5):
        self.proxy_url = proxy_url.format(num=num, minute=minute)
        self.proxy_list: List[str] = []
        self.last_update = time.time()
        self.update_interval = 120  # 2分钟更新一次代理列表
        self.logger = logging.getLogger(self.__class__.__name__)

    def update_proxy_list(self):
        """从API获取新代理并更新列表"""
        try:
            response = requests.get(self.proxy_url, timeout=10)
            if response.status_code == 200:
                text = response.text
                # 检查响应内容有效性
                if '未加入白名单' in text or '认证失败' in text:
                    self.logger.error(f"代理API返回错误: {text[:100]}")
                    return

                # 解析代理列表
                proxies = [line.strip() for line in text.split('\n') if line.strip()]
                if proxies:
                    self.proxy_list = proxies
                    self.last_update = time.time()
                    self.logger.info(f"成功更新代理列表，获取到{len(proxies)}个代理")
                else:
                    self.logger.warning("代理API返回空列表")
            else:
                self.logger.error(f"代理API请求失败，状态码: {response.status_code}")
        except Exception as e:
            self.logger.error(f"更新代理列表失败: {e}")

    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        # 检查代理列表是否需要更新
        current_time = time.time()
        need_update = not self.proxy_list or (current_time - self.last_update) > self.update_interval
        if need_update:
            self.update_proxy_list()
        
        if not self.proxy_list:
            self.logger.warning("代理列表为空，返回None")
            return None
        
        try:
            proxy = random.choice(self.proxy_list)
            return proxy
        except Exception as e:
            self.logger.error(f"获取代理时出错: {e}")
            return None

    def remove_proxy(self, proxy: str):
        """移除失效的代理"""
        try:
            if proxy in self.proxy_list:
                self.proxy_list.remove(proxy)
                self.logger.info(f"已移除失效代理: {proxy}")
        except Exception as e:
            self.logger.error(f"移除代理时出错: {e}")

    def get_proxy_count(self) -> int:
        """获取当前代理数量"""
        return len(self.proxy_list)


class MyProxyManager:
    """备用代理管理器"""
    
    def __init__(self,
                 proxy_url="https://overseas.proxy.qg.net/get?key=A1LGFVXJ&num={num}&area=990100&isp=&format=txt&seq=\r\n&distinct=false",
                 num=1):
        self.proxy_url = proxy_url.format(num=num)
        self.proxy_list: List[str] = []
        self.last_update = time.time()
        self.update_interval = 120
        self.logger = logging.getLogger(self.__class__.__name__)

    def update_proxy_list(self):
        """从API获取新代理并更新列表"""
        try:
            response = requests.get(self.proxy_url, timeout=10)
            if response.status_code == 200:
                text = response.text
                # 检查响应内容有效性
                if '未加入白名单' in text or '认证失败' in text:
                    self.logger.error(f"代理API返回错误: {text[:100]}")
                    return

                # 解析代理列表
                proxies = [line.strip() for line in text.split('\n') if line.strip()]
                if proxies:
                    self.proxy_list = proxies
                    self.last_update = time.time()
                    self.logger.info(f"成功更新代理列表，获取到{len(proxies)}个代理")
        except Exception as e:
            self.logger.error(f"更新代理列表失败: {e}")

    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        # 检查代理列表是否需要更新
        current_time = time.time()
        need_update = not self.proxy_list or (current_time - self.last_update) > self.update_interval
        if need_update:
            self.update_proxy_list()
        
        if not self.proxy_list:
            self.logger.warning("代理列表为空，返回None")
            return None
        
        try:
            proxy = random.choice(self.proxy_list)
            # 移除相同IP的代理
            ip = proxy.split(':')[0]
            self.proxy_list = [p for p in self.proxy_list if not p.startswith(ip)]
            return proxy
        except Exception as e:
            self.logger.error(f"获取代理时出错: {e}")
            return None


if __name__ == '__main__':
    # 测试代理管理器
    logging.basicConfig(level=logging.INFO)
    proxy_manager = ProxyManager(num=5)
    proxy = proxy_manager.get_random_proxy()
    if proxy:
        print(f"获取到代理: {proxy}")
        # 测试访问谷歌
        try:
            response = requests.get("https://www.google.com", 
                                  proxies={"http": f'http://{proxy}', "https": f'http://{proxy}'}, 
                                  timeout=10)
            print(f"代理测试成功，状态码: {response.status_code}")
        except Exception as e:
            print(f"代理测试失败: {e}")
    else:
        print("未获取到代理")
